# 专项数据看板 - 文件查看器

一个基于Node.js和Express的局域网文件查看器，支持Excel和PowerPoint文件的在线预览。

## 功能特性

### 📁 文件访问与展示
- 支持读取Windows文件服务器上的Excel (.xlsx, .xls) 和PowerPoint (.pptx, .ppt) 文件
- 左侧文件列表，右侧内容展示的双栏布局
- 支持添加多个不同路径的文件
- 文件访问历史记录，程序重启后依然保留

### 🎨 界面与体验
- 简洁美观的响应式界面设计
- 支持自定义背景图片（通过代码配置）
- 现代化的UI组件和动画效果
- 移动端友好的响应式设计

### ⚡ 性能优化
- 高效的文件读取和解析
- 智能的加载状态提示
- 优化的Excel表格渲染
- PowerPoint幻灯片内容解析

### 🔒 安全特性
- 文件权限检测
- 只读文件状态提示（5秒自动消失）
- 安全的文件访问控制

### 🌐 网络访问
- 局域网内多用户同时访问
- 跨平台兼容性
- 无需安装客户端软件

## 安装和运行

### 环境要求
- Node.js 14.0 或更高版本
- Windows 操作系统（用于文件服务器访问）

### 安装步骤

1. 克隆或下载项目到本地
2. 安装依赖包：
   ```bash
   npm install
   ```

3. 启动服务器：
   ```bash
   npm start
   ```
   或者使用开发模式（自动重启）：
   ```bash
   npm run dev
   ```

4. 访问应用：
   - 本地访问：http://localhost:3000
   - 局域网访问：http://[您的IP地址]:3000

## 配置说明

### 背景图片设置
在 `server.js` 文件中修改 `CONFIG` 对象：

```javascript
const CONFIG = {
    backgroundImagePath: './public/images/background.jpg', // 背景图片路径
    historyFile: './data/history.json', // 历史记录文件
    maxHistoryItems: 50 // 最大历史记录数量
};
```

### 端口配置
默认端口为3000，可以通过环境变量修改：
```bash
PORT=8080 npm start
```

## 使用方法

### 添加文件
1. 在左侧输入框中输入完整的文件路径
2. 点击"添加文件"按钮或按回车键
3. 系统会自动检查文件状态并加载内容

### 查看历史记录
- 左侧历史记录区域显示最近访问的文件
- 点击历史记录中的文件可快速重新加载
- 历史记录按访问时间排序

### Excel文件查看
- 支持多工作表切换
- 表格数据完整展示
- 保持原始格式和结构

### PowerPoint文件查看
- 显示幻灯片基本信息
- 解析幻灯片内容和备注
- 支持多张幻灯片浏览

## 文件路径示例

```
C:\Users\<USER>\Documents\数据.xlsx
\\服务器IP\共享文件夹\报告.pptx
D:\项目文件\统计表.xls
```

## 技术架构

### 后端技术
- **Express.js** - Web服务器框架
- **XLSX** - Excel文件解析
- **pptx2json** - PowerPoint文件解析
- **fs-extra** - 文件系统操作增强
- **helmet** - 安全中间件
- **cors** - 跨域资源共享

### 前端技术
- **原生JavaScript** - 无框架依赖
- **CSS3** - 现代样式和动画
- **Font Awesome** - 图标库
- **响应式设计** - 适配各种设备

## 目录结构

```
专项数据看板/
├── server.js              # 主服务器文件
├── package.json           # 项目配置和依赖
├── README.md              # 项目说明文档
├── data/                  # 数据存储目录
│   └── history.json       # 历史记录文件
└── public/                # 静态资源目录
    ├── index.html         # 主页面
    ├── styles.css         # 样式文件
    ├── script.js          # 前端脚本
    └── images/            # 图片资源目录
        └── background.jpg # 背景图片（可选）
```

## 故障排除

### 常见问题

1. **文件无法访问**
   - 检查文件路径是否正确
   - 确认文件权限设置
   - 验证网络连接（对于网络文件）

2. **PowerPoint文件解析失败**
   - 某些复杂的PPT文件可能解析不完整
   - 系统会显示基本文件信息作为备选

3. **局域网无法访问**
   - 检查防火墙设置
   - 确认服务器IP地址
   - 验证端口是否被占用

### 性能优化建议

- 避免同时打开过大的Excel文件
- 定期清理历史记录
- 使用SSD存储提高文件读取速度

## 开发和扩展

### 添加新的文件类型支持
1. 在 `FileProcessor` 类中添加新的处理方法
2. 更新前端渲染逻辑
3. 添加相应的CSS样式

### 自定义主题
修改 `public/styles.css` 中的CSS变量和样式规则。

## 许可证

MIT License - 详见 LICENSE 文件

## 支持和反馈

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 发送邮件
- 提交 Pull Request

---

**注意：** 本应用主要用于局域网环境，请确保在安全的网络环境中使用。
