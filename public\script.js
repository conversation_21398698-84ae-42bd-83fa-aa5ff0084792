class FileViewer {
    constructor() {
        this.currentFile = null;
        this.history = [];
        this.init();
    }

    async init() {
        this.bindEvents();
        await this.loadConfig();
        await this.loadHistory();
    }

    bindEvents() {
        // 添加文件按钮事件
        document.getElementById('addFileBtn').addEventListener('click', () => {
            this.addFile();
        });

        // 文件路径输入框回车事件
        document.getElementById('filePathInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addFile();
            }
        });
    }

    async loadConfig() {
        try {
            const response = await fetch('/api/config');
            const config = await response.json();
            
            // 设置背景图片
            if (config.backgroundImagePath) {
                document.body.classList.add('custom-background');
                document.body.style.setProperty('--background-image', `url('${config.backgroundImagePath}')`);
            }
        } catch (error) {
            console.error('加载配置失败:', error);
        }
    }

    async loadHistory() {
        try {
            const response = await fetch('/api/history');
            this.history = await response.json();
            this.renderHistory();
        } catch (error) {
            console.error('加载历史记录失败:', error);
            this.showNotification('加载历史记录失败', 'error');
        }
    }

    renderHistory() {
        const historyList = document.getElementById('historyList');
        
        if (this.history.length === 0) {
            historyList.innerHTML = '<div class="no-history">暂无访问历史</div>';
            return;
        }

        historyList.innerHTML = this.history.map(item => `
            <div class="file-item" data-path="${item.path}">
                <div class="file-name">${item.name}</div>
                <div class="file-path">${item.path}</div>
                <div class="file-time">${this.formatTime(item.accessTime)}</div>
            </div>
        `).join('');

        // 绑定历史记录点击事件
        historyList.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', () => {
                const filePath = item.dataset.path;
                this.loadFile(filePath);
                this.setActiveHistoryItem(item);
            });
        });
    }

    setActiveHistoryItem(activeItem) {
        document.querySelectorAll('.file-item').forEach(item => {
            item.classList.remove('active');
        });
        activeItem.classList.add('active');
    }

    formatTime(timeString) {
        const date = new Date(timeString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    async addFile() {
        const filePathInput = document.getElementById('filePathInput');
        const filePath = filePathInput.value.trim();

        if (!filePath) {
            this.showNotification('请输入文件路径', 'warning');
            return;
        }

        // 检查文件状态
        try {
            const checkResponse = await fetch('/api/file/check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ filePath })
            });

            const fileStatus = await checkResponse.json();

            if (!fileStatus.exists) {
                this.showNotification('文件不存在，请检查路径是否正确', 'error');
                return;
            }

            if (!fileStatus.readable) {
                this.showNotification('文件不可读，请检查文件权限', 'error');
                return;
            }

            // 如果文件是只读的，显示提示
            if (fileStatus.isReadOnly) {
                this.showNotification('当前文件为只读状态', 'warning', 5000);
            }

            // 加载文件
            await this.loadFile(filePath);
            filePathInput.value = '';

        } catch (error) {
            console.error('检查文件失败:', error);
            this.showNotification('检查文件失败，请重试', 'error');
        }
    }

    async loadFile(filePath) {
        this.showLoading(true);
        this.hideWelcomeMessage();

        try {
            const response = await fetch('/api/file/read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ filePath })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || '加载文件失败');
            }

            const fileData = await response.json();
            this.currentFile = fileData;
            
            // 更新文件信息
            this.updateFileInfo(filePath, fileData);
            
            // 渲染文件内容
            this.renderFileContent(fileData);
            
            // 重新加载历史记录
            await this.loadHistory();

        } catch (error) {
            console.error('加载文件失败:', error);
            this.showNotification(error.message, 'error');
            this.showWelcomeMessage();
        } finally {
            this.showLoading(false);
        }
    }

    updateFileInfo(filePath, fileData) {
        const fileName = filePath.split('\\').pop() || filePath.split('/').pop();
        document.getElementById('currentFileName').textContent = fileName;
        
        const statusIndicator = document.getElementById('fileStatus');
        if (fileData.isReadOnly) {
            statusIndicator.textContent = '只读';
            statusIndicator.className = 'status-indicator readonly';
        } else {
            statusIndicator.textContent = '正常';
            statusIndicator.className = 'status-indicator normal';
        }
    }

    renderFileContent(fileData) {
        const contentElement = document.getElementById('fileContent');
        
        if (fileData.fileType === 'excel') {
            this.renderExcelContent(contentElement, fileData);
        } else if (fileData.fileType === 'powerpoint') {
            this.renderPowerPointContent(contentElement, fileData);
        }
        
        contentElement.classList.remove('hidden');
    }

    renderExcelContent(container, data) {
        const html = `
            <div class="excel-container">
                <div class="sheet-tabs">
                    ${data.sheets.map((sheet, index) => `
                        <div class="sheet-tab ${index === 0 ? 'active' : ''}" data-sheet="${index}">
                            ${sheet.name}
                        </div>
                    `).join('')}
                </div>
                <div class="sheet-content">
                    ${data.sheets.map((sheet, index) => `
                        <div class="sheet-data ${index === 0 ? '' : 'hidden'}" data-sheet="${index}">
                            ${this.renderExcelTable(sheet.data)}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        container.innerHTML = html;
        
        // 绑定工作表切换事件
        container.querySelectorAll('.sheet-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const sheetIndex = tab.dataset.sheet;
                this.switchExcelSheet(container, sheetIndex);
            });
        });
    }

    renderExcelTable(data) {
        if (!data || data.length === 0) {
            return '<p>该工作表没有数据</p>';
        }

        const maxCols = Math.max(...data.map(row => row.length));
        
        return `
            <table class="excel-table">
                <thead>
                    <tr>
                        ${data[0].map((cell, index) => `<th>${cell || `列${index + 1}`}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${data.slice(1).map(row => `
                        <tr>
                            ${Array.from({ length: maxCols }, (_, index) => `
                                <td>${row[index] || ''}</td>
                            `).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    switchExcelSheet(container, sheetIndex) {
        // 更新标签状态
        container.querySelectorAll('.sheet-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        container.querySelector(`[data-sheet="${sheetIndex}"]`).classList.add('active');
        
        // 切换工作表内容
        container.querySelectorAll('.sheet-data').forEach(data => {
            data.classList.add('hidden');
        });
        container.querySelector(`.sheet-data[data-sheet="${sheetIndex}"]`).classList.remove('hidden');
    }

    renderPowerPointContent(container, data) {
        let slidesHtml = '';

        if (data.slides && data.slides.length > 0) {
            slidesHtml = `
                <div class="slides-container">
                    <h3>幻灯片内容 (${data.slideCount} 张)</h3>
                    <div class="slides-list">
                        ${data.slides.map(slide => `
                            <div class="slide-item">
                                <div class="slide-header">
                                    <h4><i class="fas fa-file-powerpoint"></i> ${slide.title}</h4>
                                </div>
                                <div class="slide-content">
                                    ${Array.isArray(slide.content) ?
                                        slide.content.map(item => `<p>${item}</p>`).join('') :
                                        `<p>${slide.content || '无内容'}</p>`
                                    }
                                </div>
                                ${slide.notes ? `<div class="slide-notes"><strong>备注:</strong> ${slide.notes}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        const html = `
            <div class="powerpoint-container">
                <div class="powerpoint-header">
                    <i class="fas fa-file-powerpoint powerpoint-icon"></i>
                    <h2>${data.fileName}</h2>
                    <p class="powerpoint-message">${data.message}</p>
                </div>

                <div class="powerpoint-info">
                    <div class="info-item">
                        <strong>文件大小:</strong> ${this.formatFileSize(data.fileSize)}
                    </div>
                    <div class="info-item">
                        <strong>最后修改:</strong> ${new Date(data.lastModified).toLocaleString('zh-CN')}
                    </div>
                    ${data.slideCount ? `<div class="info-item"><strong>幻灯片数量:</strong> ${data.slideCount}</div>` : ''}
                </div>

                ${slidesHtml}
            </div>
        `;

        container.innerHTML = html;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showLoading(show) {
        const loadingElement = document.getElementById('loadingIndicator');
        if (show) {
            loadingElement.classList.remove('hidden');
        } else {
            loadingElement.classList.add('hidden');
        }
    }

    showWelcomeMessage() {
        document.getElementById('welcomeMessage').classList.remove('hidden');
        document.getElementById('fileContent').classList.add('hidden');
    }

    hideWelcomeMessage() {
        document.getElementById('welcomeMessage').classList.add('hidden');
    }

    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.getElementById('notification');
        const icon = notification.querySelector('.notification-icon');
        const text = notification.querySelector('.notification-text');
        
        // 设置图标
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        icon.className = `notification-icon ${icons[type] || icons.info}`;
        text.textContent = message;
        
        // 设置样式
        notification.className = `notification ${type}`;
        
        // 显示通知
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
        }, duration);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new FileViewer();
});
