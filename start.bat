@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    专项数据看板 - 文件查看器
echo ========================================
echo.
echo 正在启动服务器...
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未检测到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查依赖是否安装
if not exist "node_modules" (
    echo 正在安装依赖包...
    npm install
    if errorlevel 1 (
        echo 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

REM 获取本机IP地址
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    set "ip=%%a"
    goto :found
)
:found
set ip=%ip: =%

echo 启动成功！
echo.
echo 访问地址:
echo   本地访问: http://localhost:3000
echo   局域网访问: http://%ip%:3000
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================
echo.

REM 启动服务器
npm start
