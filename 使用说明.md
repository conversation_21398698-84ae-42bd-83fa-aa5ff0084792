# 专项数据看板 - 使用说明

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）
1. 双击 `start.bat` 文件
2. 等待服务器启动完成
3. 在浏览器中访问显示的地址

### 方法二：手动启动
1. 打开命令提示符
2. 进入项目目录
3. 运行 `npm start`
4. 访问 http://localhost:3000

## 📋 功能使用指南

### 1. 添加文件
- 在左侧输入框中输入完整的文件路径
- 支持的格式：
  - Excel文件：`.xlsx`, `.xls`
  - PowerPoint文件：`.pptx`, `.ppt`
- 点击"添加文件"按钮或按回车键

### 2. 文件路径示例
```
本地文件：
D:\数据\销售报表.xlsx
C:\Users\<USER>\Documents\项目汇报.pptx

网络文件：
\\*************\共享文件夹\月度统计.xlsx
\\服务器名\部门文件\年度总结.pptx
```

### 3. 查看历史记录
- 左侧显示最近访问的文件
- 点击历史记录可快速重新打开文件
- 历史记录会自动保存，重启程序后依然存在

### 4. Excel文件查看
- 支持多工作表切换
- 点击顶部标签页切换不同工作表
- 表格数据完整显示，保持原始格式

### 5. PowerPoint文件查看
- 显示文件基本信息
- 解析幻灯片内容和备注
- 支持多张幻灯片内容展示

## ⚠️ 注意事项

### 文件权限
- 确保有文件读取权限
- 只读文件会显示特殊提示
- 网络文件需要相应的网络访问权限

### 文件大小
- 建议单个Excel文件不超过50MB
- 过大的文件可能影响加载速度
- PowerPoint文件解析可能需要较长时间

### 网络访问
- 局域网用户可通过IP地址访问
- 确保防火墙允许3000端口
- 建议在安全的内网环境中使用

## 🔧 故障排除

### 常见问题

**1. 文件无法打开**
- 检查文件路径是否正确
- 确认文件是否存在
- 验证文件权限设置

**2. 网络文件访问失败**
- 检查网络连接
- 确认共享文件夹权限
- 尝试在文件管理器中手动访问

**3. 页面无法加载**
- 检查服务器是否正常运行
- 确认端口3000未被占用
- 尝试重启应用程序

**4. Excel表格显示异常**
- 某些复杂格式可能不完全支持
- 建议使用标准的Excel格式
- 避免过于复杂的公式和宏

## 🎨 自定义设置

### 背景图片设置
1. 将背景图片放入 `public/images/` 目录
2. 编辑 `server.js` 文件中的配置：
```javascript
const CONFIG = {
    backgroundImagePath: './public/images/你的背景图片.jpg'
};
```
3. 重启服务器

### 端口修改
- 默认端口：3000
- 修改方法：设置环境变量 `PORT=新端口号`
- 或在 `server.js` 中修改 `PORT` 常量

### 历史记录数量
在 `server.js` 中修改：
```javascript
const CONFIG = {
    maxHistoryItems: 100  // 修改为你想要的数量
};
```

## 📁 测试文件

项目包含了两个测试Excel文件：
1. `test-files/销售数据统计.xlsx` - 包含销售数据、员工信息、月度统计
2. `test-files/项目管理表.xlsx` - 包含项目进度信息

可以使用这些文件测试应用功能。

## 🔒 安全建议

1. **仅在内网使用** - 不建议暴露到公网
2. **文件权限控制** - 确保只能访问授权文件
3. **定期更新** - 保持依赖包的最新版本
4. **备份重要数据** - 应用仅用于查看，不会修改原文件

## 📞 技术支持

如遇到问题，请检查：
1. Node.js版本是否为14.0或更高
2. 网络连接是否正常
3. 文件路径是否正确
4. 防火墙设置是否允许

---

**提示：** 首次使用建议先用测试文件验证功能是否正常，然后再使用实际的业务文件。
