/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: #333;
}

/* 背景图片支持 */
body.custom-background {
    background-image: var(--background-image);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.container {
    display: flex;
    height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 左侧边栏 */
.sidebar {
    width: 350px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
    font-size: 18px;
    font-weight: 600;
}

.sidebar-header i {
    margin-right: 8px;
    color: #3498db;
}

/* 文件输入区域 */
.file-input-section {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-group input {
    padding: 12px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.input-group input:focus {
    outline: none;
    background: white;
    box-shadow: 0 0 0 2px #3498db;
}

.btn-primary {
    padding: 12px 16px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* 历史记录区域 */
.history-section {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.history-section h3 {
    font-size: 16px;
    margin-bottom: 15px;
    color: #ecf0f1;
}

.file-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-item {
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.file-item:hover {
    background: rgba(255, 255, 255, 0.2);
    border-left-color: #3498db;
    transform: translateX(4px);
}

.file-item.active {
    background: rgba(52, 152, 219, 0.3);
    border-left-color: #3498db;
}

.file-name {
    font-weight: 500;
    margin-bottom: 4px;
    color: #ecf0f1;
}

.file-path {
    font-size: 12px;
    color: #bdc3c7;
    word-break: break-all;
}

.file-time {
    font-size: 11px;
    color: #95a5a6;
    margin-top: 4px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.content-header {
    padding: 20px 30px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-header h1 {
    font-size: 24px;
    color: #2c3e50;
    font-weight: 600;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-indicator {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator.readonly {
    background: #ffeaa7;
    color: #d63031;
}

.status-indicator.normal {
    background: #d1f2eb;
    color: #00b894;
}

/* 内容主体 */
.content-body {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    position: relative;
}

/* 欢迎消息 */
.welcome-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.welcome-content {
    max-width: 500px;
}

.welcome-icon {
    font-size: 64px;
    color: #3498db;
    margin-bottom: 20px;
}

.welcome-content h2 {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 15px;
}

.welcome-content p {
    font-size: 16px;
    color: #7f8c8d;
    margin-bottom: 30px;
}

.supported-formats h3 {
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 15px;
}

.format-list {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.format-item {
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 6px;
    color: #495057;
    font-size: 14px;
}

.format-item i {
    margin-right: 8px;
    color: #3498db;
}

/* 加载指示器 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    font-size: 18px;
    color: #3498db;
}

.loading i {
    margin-right: 10px;
    font-size: 24px;
}

/* 文件内容展示 */
.file-content {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Excel表格样式 */
.excel-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sheet-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    overflow-x: auto;
}

.sheet-tab {
    padding: 12px 20px;
    cursor: pointer;
    border-right: 1px solid #dee2e6;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.sheet-tab:hover {
    background: #e9ecef;
}

.sheet-tab.active {
    background: white;
    border-bottom: 2px solid #3498db;
}

.sheet-content {
    padding: 20px;
    overflow-x: auto;
}

.excel-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.excel-table th,
.excel-table td {
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    text-align: left;
}

.excel-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.excel-table tr:nth-child(even) {
    background: #f8f9fa;
}

/* PowerPoint样式 */
.powerpoint-container {
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.powerpoint-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.powerpoint-icon {
    font-size: 64px;
    color: #d04423;
    margin-bottom: 20px;
}

.powerpoint-header h2 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.powerpoint-message {
    color: #7f8c8d;
    font-size: 16px;
}

.powerpoint-info {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
}

.info-item {
    text-align: center;
    color: #495057;
}

.slides-container {
    margin-top: 30px;
}

.slides-container h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.slides-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.slide-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.slide-header {
    background: linear-gradient(135deg, #d04423, #b8371f);
    color: white;
    padding: 15px 20px;
}

.slide-header h4 {
    margin: 0;
    font-size: 16px;
}

.slide-header i {
    margin-right: 8px;
}

.slide-content {
    padding: 20px;
    line-height: 1.6;
}

.slide-content p {
    margin-bottom: 10px;
    color: #495057;
}

.slide-notes {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    color: #6c757d;
    font-style: italic;
}

/* 通知消息 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.error {
    border-left: 4px solid #e74c3c;
}

.notification.warning {
    border-left: 4px solid #f39c12;
}

.notification.success {
    border-left: 4px solid #27ae60;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-icon {
    font-size: 18px;
}

.notification.error .notification-icon {
    color: #e74c3c;
}

.notification.warning .notification-icon {
    color: #f39c12;
}

.notification.success .notification-icon {
    color: #27ae60;
}

/* 工具类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        height: auto;
        min-height: 100vh;
    }

    .sidebar {
        width: 100%;
        height: auto;
    }

    .content-header {
        padding: 15px 20px;
    }

    .content-header h1 {
        font-size: 20px;
    }

    .content-body {
        padding: 20px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
