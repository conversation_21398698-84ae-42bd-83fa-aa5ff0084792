const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

// 创建测试目录
const testDir = './test-files';
if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir);
}

// 创建示例Excel文件
function createTestExcel() {
    const workbook = XLSX.utils.book_new();
    
    // 第一个工作表 - 销售数据
    const salesData = [
        ['产品名称', '销售数量', '单价', '总金额', '销售日期'],
        ['笔记本电脑', 15, 5999, 89985, '2024-01-15'],
        ['台式机', 8, 4299, 34392, '2024-01-16'],
        ['显示器', 25, 1299, 32475, '2024-01-17'],
        ['键盘', 50, 199, 9950, '2024-01-18'],
        ['鼠标', 45, 89, 4005, '2024-01-19'],
        ['音响', 12, 599, 7188, '2024-01-20'],
        ['摄像头', 18, 299, 5382, '2024-01-21']
    ];
    
    const salesSheet = XLSX.utils.aoa_to_sheet(salesData);
    XLSX.utils.book_append_sheet(workbook, salesSheet, '销售数据');
    
    // 第二个工作表 - 员工信息
    const employeeData = [
        ['员工编号', '姓名', '部门', '职位', '入职日期', '薪资'],
        ['E001', '张三', '销售部', '销售经理', '2023-03-15', 8000],
        ['E002', '李四', '技术部', '软件工程师', '2023-05-20', 12000],
        ['E003', '王五', '市场部', '市场专员', '2023-07-10', 6000],
        ['E004', '赵六', '人事部', '人事主管', '2023-02-01', 7500],
        ['E005', '钱七', '财务部', '会计', '2023-04-12', 6500]
    ];
    
    const employeeSheet = XLSX.utils.aoa_to_sheet(employeeData);
    XLSX.utils.book_append_sheet(workbook, employeeSheet, '员工信息');
    
    // 第三个工作表 - 月度统计
    const monthlyData = [
        ['月份', '销售额', '成本', '利润', '利润率'],
        ['2024-01', 156789, 98456, 58333, '37.2%'],
        ['2024-02', 189234, 112567, 76667, '40.5%'],
        ['2024-03', 234567, 145678, 88889, '37.9%'],
        ['2024-04', 198765, 123456, 75309, '37.9%'],
        ['2024-05', 267890, 167890, 100000, '37.3%'],
        ['2024-06', 298765, 187654, 111111, '37.2%']
    ];
    
    const monthlySheet = XLSX.utils.aoa_to_sheet(monthlyData);
    XLSX.utils.book_append_sheet(workbook, monthlySheet, '月度统计');
    
    // 保存文件
    const excelPath = path.join(testDir, '销售数据统计.xlsx');
    XLSX.writeFile(workbook, excelPath);
    console.log(`Excel测试文件已创建: ${excelPath}`);
    
    return excelPath;
}

// 创建另一个Excel文件
function createTestExcel2() {
    const workbook = XLSX.utils.book_new();
    
    const projectData = [
        ['项目名称', '负责人', '开始日期', '结束日期', '状态', '完成度'],
        ['网站重构项目', '张工程师', '2024-01-01', '2024-03-31', '进行中', '75%'],
        ['移动应用开发', '李开发', '2024-02-15', '2024-06-30', '进行中', '45%'],
        ['数据库优化', '王DBA', '2024-01-15', '2024-02-28', '已完成', '100%'],
        ['安全审计', '赵安全', '2024-03-01', '2024-04-15', '计划中', '0%'],
        ['用户培训', '钱培训师', '2024-04-01', '2024-05-31', '计划中', '0%']
    ];
    
    const projectSheet = XLSX.utils.aoa_to_sheet(projectData);
    XLSX.utils.book_append_sheet(workbook, projectSheet, '项目进度');
    
    const excelPath = path.join(testDir, '项目管理表.xlsx');
    XLSX.writeFile(workbook, excelPath);
    console.log(`Excel测试文件已创建: ${excelPath}`);
    
    return excelPath;
}

// 创建测试文件
try {
    const excel1 = createTestExcel();
    const excel2 = createTestExcel2();
    
    console.log('\n测试文件创建完成！');
    console.log('您可以使用以下路径测试应用：');
    console.log(`1. ${path.resolve(excel1)}`);
    console.log(`2. ${path.resolve(excel2)}`);
    console.log('\n请在网页中输入完整路径来测试文件查看功能。');
    
} catch (error) {
    console.error('创建测试文件失败:', error);
}
