const express = require('express');
const fs = require('fs-extra');
const path = require('path');
const cors = require('cors');
const helmet = require('helmet');
const XLSX = require('xlsx');
const pptx2json = require('pptx2json');

const app = express();
const PORT = process.env.PORT || 3000;

// 配置变量 - 可以在这里设置背景图片路径
const CONFIG = {
    backgroundImagePath: './public/images/background.jpg', // 背景图片路径
    historyFile: './data/history.json', // 历史记录文件
    maxHistoryItems: 50 // 最大历史记录数量
};

// 中间件配置
app.use(helmet({
    contentSecurityPolicy: false // 允许内联样式和脚本
}));
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static('public'));

// 确保数据目录存在
fs.ensureDirSync('./data');
fs.ensureDirSync('./public/images');

// 历史记录管理
class HistoryManager {
    constructor() {
        this.historyFile = CONFIG.historyFile;
        this.loadHistory();
    }

    loadHistory() {
        try {
            if (fs.existsSync(this.historyFile)) {
                this.history = fs.readJsonSync(this.historyFile);
            } else {
                this.history = [];
            }
        } catch (error) {
            console.error('加载历史记录失败:', error);
            this.history = [];
        }
    }

    saveHistory() {
        try {
            fs.writeJsonSync(this.historyFile, this.history, { spaces: 2 });
        } catch (error) {
            console.error('保存历史记录失败:', error);
        }
    }

    addToHistory(filePath) {
        const existingIndex = this.history.findIndex(item => item.path === filePath);
        const historyItem = {
            path: filePath,
            name: path.basename(filePath),
            accessTime: new Date().toISOString()
        };

        if (existingIndex !== -1) {
            this.history.splice(existingIndex, 1);
        }

        this.history.unshift(historyItem);
        
        if (this.history.length > CONFIG.maxHistoryItems) {
            this.history = this.history.slice(0, CONFIG.maxHistoryItems);
        }

        this.saveHistory();
    }

    getHistory() {
        return this.history;
    }
}

const historyManager = new HistoryManager();

// 文件处理工具
class FileProcessor {
    static async checkFileAccess(filePath) {
        try {
            await fs.access(filePath, fs.constants.F_OK);
            
            // 检查是否为只读
            const stats = await fs.stat(filePath);
            const isReadOnly = !(stats.mode & parseInt('200', 8));
            
            return {
                exists: true,
                readable: true,
                isReadOnly: isReadOnly
            };
        } catch (error) {
            return {
                exists: false,
                readable: false,
                isReadOnly: false,
                error: error.message
            };
        }
    }

    static async processExcelFile(filePath) {
        try {
            const workbook = XLSX.readFile(filePath);
            const result = {
                sheets: [],
                metadata: {
                    fileName: path.basename(filePath),
                    sheetCount: workbook.SheetNames.length
                }
            };

            workbook.SheetNames.forEach(sheetName => {
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                
                result.sheets.push({
                    name: sheetName,
                    data: jsonData
                });
            });

            return result;
        } catch (error) {
            throw new Error(`Excel文件处理失败: ${error.message}`);
        }
    }

    static async processPowerPointFile(filePath) {
        try {
            const stats = await fs.stat(filePath);
            const result = {
                fileName: path.basename(filePath),
                fileSize: stats.size,
                lastModified: stats.mtime,
                slides: []
            };

            // 尝试解析PowerPoint文件
            try {
                const pptData = await pptx2json.toJson(filePath);

                if (pptData && pptData.slides) {
                    result.slides = pptData.slides.map((slide, index) => ({
                        slideNumber: index + 1,
                        title: slide.title || `幻灯片 ${index + 1}`,
                        content: slide.content || [],
                        notes: slide.notes || ''
                    }));
                    result.slideCount = result.slides.length;
                    result.message = `成功解析PowerPoint文件，共 ${result.slideCount} 张幻灯片`;
                } else {
                    result.message = 'PowerPoint文件解析成功，但未找到幻灯片内容';
                }
            } catch (parseError) {
                console.warn('PowerPoint解析失败，返回基本信息:', parseError.message);
                result.message = 'PowerPoint文件基本信息（解析内容失败）';
            }

            return result;
        } catch (error) {
            throw new Error(`PowerPoint文件处理失败: ${error.message}`);
        }
    }
}

// API路由

// 获取配置信息
app.get('/api/config', (req, res) => {
    res.json({
        backgroundImagePath: CONFIG.backgroundImagePath
    });
});

// 获取历史记录
app.get('/api/history', (req, res) => {
    res.json(historyManager.getHistory());
});

// 检查文件状态
app.post('/api/file/check', async (req, res) => {
    try {
        const { filePath } = req.body;
        
        if (!filePath) {
            return res.status(400).json({ error: '文件路径不能为空' });
        }

        const fileStatus = await FileProcessor.checkFileAccess(filePath);
        res.json(fileStatus);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 读取文件内容
app.post('/api/file/read', async (req, res) => {
    try {
        const { filePath } = req.body;
        
        if (!filePath) {
            return res.status(400).json({ error: '文件路径不能为空' });
        }

        // 检查文件访问权限
        const fileStatus = await FileProcessor.checkFileAccess(filePath);
        
        if (!fileStatus.exists) {
            return res.status(404).json({ error: '文件不存在' });
        }

        if (!fileStatus.readable) {
            return res.status(403).json({ error: '文件不可读' });
        }

        // 添加到历史记录
        historyManager.addToHistory(filePath);

        const ext = path.extname(filePath).toLowerCase();
        let result;

        if (['.xlsx', '.xls'].includes(ext)) {
            result = await FileProcessor.processExcelFile(filePath);
            result.fileType = 'excel';
        } else if (['.pptx', '.ppt'].includes(ext)) {
            result = await FileProcessor.processPowerPointFile(filePath);
            result.fileType = 'powerpoint';
        } else {
            return res.status(400).json({ error: '不支持的文件类型' });
        }

        result.isReadOnly = fileStatus.isReadOnly;
        res.json(result);

    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
    console.log(`服务器运行在 http://0.0.0.0:${PORT}`);
    console.log(`局域网用户可通过 http://[您的IP地址]:${PORT} 访问`);
});
